// Create Template Popup Styles
.create-template-popup {
  .k-dialog {
    // width: 800px;
    // max-width: 90vw;
    // height: 350px; // Reduced height
    // max-height: 80vh;
    // border: none; // Remove any border
  }

  // More specific selectors to override global theme
  &.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar,
  .k-dialog-titlebar {
    background-color: #007acc; // Blue header background
    // color: white;
    // border: none; // Remove white border line
    // border-bottom: none; // Ensure no bottom border
    border-top: none; // Remove top border
    // border-left: none; // Remove left border
    // border-right: none; // Remove right border
    // padding: 16px 20px;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;

    .k-dialog-title,
    .k-window-title.k-dialog-title {
      color: white;
      font-weight: 600;
      font-size: 18px;
      margin: 0;
    }

    .k-dialog-close,
    .k-window-titlebar-action.k-dialog-titlebar-action {
      // color: white;
      // display: flex; // Ensure close icon is visible
      // align-items: center;
      // justify-content: center;
      // width: 24px;
      // height: 24px;
      // border: none;
      // background: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .k-icon,
      .k-button-icon {
        color: white;
      }
    }
  }

  .k-dialog-content {
    padding: 0;
    // overflow: hidden;
    // display: flex;
    // height: calc(100% - 100px); // Adjusted for reduced height
  }

  .popup-content {
    display: flex;
    width: 100%;
    height: 100%;

    .popup-left {
      flex: 1;
      padding: 20px;
      border-right: 1px solid #e0e0e0; // Border separation
      // background-color: white;
      // overflow-y: auto;
      // display: flex;
      // flex-direction: column;
      // gap: 16px;
    }

    .popup-right {
      flex: 1;
      padding: 20px;
      background-color: white;
      // display: flex;
      // align-items: center;
      // justify-content: center;

      .preview-placeholder {
        text-align: center;
        color: #666;
        font-style: italic;
      }
    }
  }

  .k-dialog-actions {
    padding: 12px 20px; // Reduced padding
    border-top: 1px solid #e0e0e0;
    background-color: white;
    display: flex;
    justify-content: flex-end; // Align buttons to the right
    gap: 8px; // Reduced gap

    .k-button {
      padding: 8px 16px; // Smaller button padding
      font-weight: 500;
      border-radius: 4px;
      min-width: 80px; // Reduced min-width
      font-size: 14px; // Smaller font size

      &.k-button-solid-primary {
        background-color: #007acc;
        border-color: #007acc;
        color: white;

        &:hover {
          background-color: #005a99;
          border-color: #005a99;
        }
      }

      &.k-button-solid-base {
        background-color: transparent;
        border: none;
        color: #333;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

// Form Styles
.create-template-form {
  .form-group {
    margin-bottom: 16px; // Reduced spacing for better fit

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .validation-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
    // display: block;
  }

  .switch-group {
    .switch-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 100%;

      .switch-label {
        // font-weight: 500;
        // color: #333;
        // font-size: 14px;
      }

      .k-switch {
        // margin-left: auto; // Push switch to the right
      }
    }
  }
}

