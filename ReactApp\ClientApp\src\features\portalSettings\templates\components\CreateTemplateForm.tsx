import { Input, Switch } from "@progress/kendo-react-inputs";
import type { CreateTemplateFormData, CreateTemplateValidation } from "../hooks/useCreateTemplatePopup";

interface CreateTemplateFormProps {
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  onFieldChange: (field: keyof CreateTemplateFormData, value: string | boolean) => void;
}

export default function CreateTemplateForm({
  formData,
  validation,
  onFieldChange,
}: CreateTemplateFormProps) {

  return (
    <div className="create-template-form">
      {/* Name Field */}
      <div className="form-group">
        <label htmlFor="template-name" className="form-label">
          Name<span style={{color: "red"}}>*</span>
        </label>
        <Input
          id="template-name"
          name="name"
          value={formData.name}
          onChange={(e) => onFieldChange("name", e.value)}
          placeholder="Enter template name"
          style={{ 
            width: "100%",
            borderColor: validation.nameError ? "#dc3545" : undefined
          }}
        />
        {validation.nameError && (
          <div className="validation-error">
            {validation.nameError}
          </div>
        )}
      </div>

      {/* Description Field */}
      <div className="form-group">
        <label htmlFor="template-description" className="form-label">
          Description
        </label>
        <Input
          id="template-description"
          name="description"
          value={formData.description}
          onChange={(e) => onFieldChange("description", e.value)}
          placeholder="Enter description"
          style={{ width: "100%" }}
        />
      </div>

      {/* Active Switch */}
      <div className="form-group switch-group">
        <div className="switch-container">
          <span className="switch-label">
            {formData.isActive ? "Active" : "Inactive"}
          </span>
          <Switch
            onLabel=""
            offLabel=""
            checked={formData.isActive}
            onChange={(e) => onFieldChange("isActive", e.value)}
          />
        </div>
      </div>
    </div>
  );
}
